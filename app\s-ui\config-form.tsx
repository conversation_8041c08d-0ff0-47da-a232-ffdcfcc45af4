import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import { SafeAreaView, StyleSheet, View } from 'react-native';

import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { ConfigFormData } from '@/lib/types';
import { SUIConfigForm } from '~/panels/s-ui';

export default function SUIConfigFormScreen() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const { addConfig, updateConfig } = useAppStore();
  const { initialData } = useLocalSearchParams<{ initialData: string }>();
  const { configId } = useLocalSearchParams<{ configId: string }>();

  const isEditMode = !!configId;

  const handleFormSubmit = async (formData: ConfigFormData) => {
    try {
      if (isEditMode && configId) {
        // 编辑模式：使用updateConfig方法处理分组变更
        const selectedGroupIds = Array.isArray(formData.groupId) ? formData.groupId : [formData.groupId].filter(Boolean);

        await updateConfig(configId, {
          name: formData.name,
          url: formData.url.replace(/\/+$/, ''), // 删除末尾的斜杠
          protocol: formData.protocol,
          cert: formData.cert,
          certFingerprints: formData.certFingerprints || [],
          api: formData.api!,
        }, selectedGroupIds);
      } else {
        // 新增模式：使用addConfig方法
        await addConfig(formData, 's-ui');
      }

      // 关闭模态框并返回主页
      router.back();
    } catch (error) {
      console.error(`Failed to ${isEditMode ? 'update' : 'add'} s-ui config:`, error);
      // 这里可以添加错误提示
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.content}>
        <SUIConfigForm
          onSubmit={handleFormSubmit}
          configId={configId ?? undefined}
          initialData={initialData ?? undefined}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  content: {
    flex: 1,
  },
});
